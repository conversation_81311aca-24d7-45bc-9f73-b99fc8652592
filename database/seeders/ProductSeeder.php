<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\ProductClass;
use App\Models\Brand;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, ensure we have the required data
        $this->ensureRequiredData();
        
        // Create realistic UAE marketplace products
        $this->createHealthSupplementProducts();
        $this->createBeautyProducts();
        $this->createFoodProducts();
        $this->createSportsNutritionProducts();
        $this->createWeightManagementProducts();
    }

    private function ensureRequiredData(): void
    {
        // Create vendors if they don't exist
        if (Vendor::count() === 0) {
            $this->createVendors();
        }
    }

    private function createVendors(): void
    {
         $vendors = [
        [
            'code' => 'VND001',
            'vendor_eoi_id' => null,
            'name_tl_en' => 'HealthPlus UAE',
            'name_tl_ar' => 'هيلث بلس امارات',
            'vendor_display_name_en' => 'HealthPlus',
            'vendor_display_name_ar' => 'هيلث بلس',
            'business_type' => json_encode(['distributor']),
            'tl_license_issuing_authority' => 'Dubai Economic Department',
            'tl_license_first_issue_date' => Carbon::now()->subYears(3),
            'tl_license_valid_till' => Carbon::now()->addYears(2),
            'tax_registration_number' => '100123456789003',
            'director_name' => 'Ahmed Al Mansouri',
            'director_email' => '<EMAIL>',
            'director_mobile' => '+971501234567',
            'approval_status' => 'Approved',
            'is_active' => true,
        ],
        [
            'code' => 'VND002',
            'vendor_eoi_id' => null,
            'name_tl_en' => 'Beauty Emirates',
            'name_tl_ar' => 'بيوتي إمارات',
            'vendor_display_name_en' => 'Beauty Emirates',
            'vendor_display_name_ar' => 'بيوتي إمارات',
            'business_type' => json_encode(['retailer']),
            'tl_license_issuing_authority' => 'Abu Dhabi Economic Department',
            'tl_license_first_issue_date' => Carbon::now()->subYears(2),
            'tl_license_valid_till' => Carbon::now()->addYears(3),
            'tax_registration_number' => '100123456789004',
            'director_name' => 'Fatima Al Zahra',
            'director_email' => '<EMAIL>',
            'director_mobile' => '+971507654321',
            'approval_status' => 'Approved',
            'is_active' => true,
        ],
        [
            'code' => 'VND003',
            'vendor_eoi_id' => null,
            'name_tl_en' => 'Nutrition Hub',
            'name_tl_ar' => 'نيوتريشن هب',
            'vendor_display_name_en' => 'Nutrition Hub',
            'vendor_display_name_ar' => 'نيوتريشن هب',
            'business_type' => json_encode(['importer']),
            'tl_license_issuing_authority' => 'Sharjah Economic Department',
            'tl_license_first_issue_date' => Carbon::now()->subYears(4),
            'tl_license_valid_till' => Carbon::now()->addYears(1),
            'tax_registration_number' => '100123456789005',
            'director_name' => 'Mohammed Al Rashid',
            'director_email' => '<EMAIL>',
            'director_mobile' => '+971509876543',
            'approval_status' => 'Approved',
            'is_active' => true,
        ],
    ];


        foreach ($vendors as $vendorData) {
            Vendor::create($vendorData);
        }
    }

    private function createHealthSupplementProducts(): void
    {
        $healthCategory = Category::where('code', 'H')->first();
        $vitaminSubcategory = Category::where('code', 'VS')->first();
        $herbsSubcategory = Category::where('code', 'HS')->first();
        
        $vendor = Vendor::where('code', 'VND001')->first();
        $user = User::where('email', '<EMAIL>')->first();
        $brands = Brand::where('status', 'approved')->limit(3)->get();

        $healthProducts = [
            [
                'title_en' => 'Vitamin D3 5000 IU',
                'title_ar' => 'فيتامين د3 5000 وحدة دولية',
                'short_name' => 'Vitamin D3',
                'short_description_en' => 'High potency Vitamin D3 for bone health and immune support',
                'short_description_ar' => 'فيتامين د3 عالي الفعالية لصحة العظام ودعم المناعة',
                'description_en' => 'Premium Vitamin D3 supplement providing 5000 IU per capsule. Essential for calcium absorption, bone health, and immune system function. Made from high-quality cholecalciferol.',
                'description_ar' => 'مكمل فيتامين د3 الممتاز يوفر 5000 وحدة دولية لكل كبسولة. ضروري لامتصاص الكالسيوم وصحة العظام ووظيفة الجهاز المناعي.',
                'category_id' => $healthCategory->id,
                'sub_category_id' => $vitaminSubcategory->id,
                'regular_price' => 89.00,
                'offer_price' => 75.00,
                'net_weight' => 120,
                'net_weight_unit' => 'capsules',
                'servings' => 120,
                'country_of_origin' => 'USA',
                'is_halal' => true,
                'is_vegetarian' => true,
                'allergen_info' => 'Contains gelatin',
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 6.5,
                'package_width' => 6.5,
                'package_height' => 12.0,
                'package_weight' => 0.15,
            ],
            [
                'title_en' => 'Omega-3 Fish Oil 1000mg',
                'title_ar' => 'زيت السمك أوميغا-3 1000 ملغ',
                'short_name' => 'Omega-3',
                'short_description_en' => 'Pure fish oil with EPA and DHA for heart and brain health',
                'short_description_ar' => 'زيت السمك النقي مع EPA و DHA لصحة القلب والدماغ',
                'description_en' => 'High-quality fish oil supplement providing 1000mg of omega-3 fatty acids per softgel. Rich in EPA and DHA to support cardiovascular health, brain function, and joint mobility.',
                'description_ar' => 'مكمل زيت السمك عالي الجودة يوفر 1000 ملغ من أحماض أوميغا-3 الدهنية لكل كبسولة جيلاتينية.',
                'category_id' => $healthCategory->id,
                'sub_category_id' => Category::where('code', 'FO')->first()->id,
                'regular_price' => 125.00,
                'offer_price' => 99.00,
                'net_weight' => 90,
                'net_weight_unit' => 'softgels',
                'servings' => 90,
                'country_of_origin' => 'Norway',
                'is_halal' => false,
                'allergen_info' => 'Contains fish',
                'storage_conditions' => 'Refrigerate after opening',
                'package_length' => 7.0,
                'package_width' => 7.0,
                'package_height' => 13.5,
                'package_weight' => 0.18,
            ],
        ];

        foreach ($healthProducts as $index => $productData) {
            $productData['user_id'] = $user->id;
            $productData['vendor_id'] = $vendor->id;
            $productData['brand_id'] = $brands[$index % $brands->count()]->id;
            $productData['vendor_sku'] = 'HP-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $productData['barcode'] = '629' . str_pad($index + 1, 10, '0', STR_PAD_LEFT);
            $productData['vat_tax'] = 'standard_5';
            $productData['discount_start_date'] = Carbon::now();
            $productData['discount_end_date'] = Carbon::now()->addDays(30);
            $productData['bbe_date'] = Carbon::now()->addYears(2);
            $productData['is_active'] = true;
            $productData['is_approved'] = true;
            $productData['status'] = 'submitted';

            Product::create($productData);
        }
    }

    private function createBeautyProducts(): void
    {
        $beautyCategory = Category::where('code', 'B')->first();
        $skinCareSubcategory = Category::where('code', 'SC')->first();

        $vendor = Vendor::where('code', 'VND002')->first();
        $user = User::where('email', '<EMAIL>')->first();
        $brands = Brand::where('status', 'approved')->skip(1)->limit(2)->get();

        $beautyProducts = [
            [
                'title_en' => 'Hyaluronic Acid Serum',
                'title_ar' => 'سيروم حمض الهيالورونيك',
                'short_name' => 'HA Serum',
                'short_description_en' => 'Intensive hydrating serum with pure hyaluronic acid',
                'short_description_ar' => 'سيروم مرطب مكثف بحمض الهيالورونيك النقي',
                'description_en' => 'Advanced anti-aging serum with multiple molecular weights of hyaluronic acid. Provides deep hydration, plumps fine lines, and improves skin texture for a youthful glow.',
                'description_ar' => 'سيروم مضاد للشيخوخة متقدم بأوزان جزيئية متعددة من حمض الهيالورونيك.',
                'category_id' => $beautyCategory->id,
                'sub_category_id' => $skinCareSubcategory->id,
                'regular_price' => 199.00,
                'offer_price' => 159.00,
                'net_weight' => 30,
                'net_weight_unit' => 'ml',
                'country_of_origin' => 'South Korea',
                'is_vegan' => true,
                'is_vegetarian' => true,
                'allergen_info' => 'Fragrance-free, paraben-free',
                'storage_conditions' => 'Store in cool place, avoid direct sunlight',
                'package_length' => 4.0,
                'package_width' => 4.0,
                'package_height' => 10.5,
                'package_weight' => 0.08,
            ],
        ];

        foreach ($beautyProducts as $index => $productData) {
            $productData['user_id'] = $user->id;
            $productData['vendor_id'] = $vendor->id;
            $productData['brand_id'] = $brands[$index % $brands->count()]->id;
            $productData['vendor_sku'] = 'BE-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $productData['barcode'] = '880' . str_pad($index + 1, 10, '0', STR_PAD_LEFT);
            $productData['vat_tax'] = 'standard_5';
            $productData['discount_start_date'] = Carbon::now();
            $productData['discount_end_date'] = Carbon::now()->addDays(15);
            $productData['bbe_date'] = Carbon::now()->addYears(3);
            $productData['is_active'] = true;
            $productData['is_approved'] = true;
            $productData['status'] = 'submitted';

            Product::create($productData);
        }
    }

    private function createFoodProducts(): void
    {
        $foodCategory = Category::where('code', 'F')->first();
        $healthFoodSubcategory = Category::where('code', 'HF')->first();

        $vendor = Vendor::where('code', 'VND003')->first();
        $user = User::where('email', '<EMAIL>')->first();
        $brands = Brand::where('status', 'approved')->limit(2)->get();

        $foodProducts = [
            [
                'title_en' => 'Organic Manuka Honey',
                'title_ar' => 'عسل مانوكا العضوي',
                'short_name' => 'Manuka Honey',
                'short_description_en' => 'Premium organic Manuka honey from New Zealand',
                'short_description_ar' => 'عسل مانوكا العضوي الممتاز من نيوزيلندا',
                'description_en' => 'Authentic Manuka honey with UMF 15+ rating. Raw, unprocessed honey with natural antibacterial properties. Perfect for immune support and digestive health.',
                'description_ar' => 'عسل مانوكا الأصلي بتقييم UMF 15+. عسل خام غير معالج بخصائص مضادة للبكتيريا طبيعية.',
                'category_id' => $foodCategory->id,
                'sub_category_id' => Category::where('code', 'HS')->first()->id,
                'regular_price' => 299.00,
                'offer_price' => 249.00,
                'net_weight' => 500,
                'net_weight_unit' => 'g',
                'country_of_origin' => 'New Zealand',
                'is_halal' => true,
                'is_vegan' => false,
                'is_vegetarian' => true,
                'allergen_info' => 'Not suitable for children under 12 months',
                'storage_conditions' => 'Store at room temperature, avoid direct sunlight',
                'package_length' => 8.5,
                'package_width' => 8.5,
                'package_height' => 12.0,
                'package_weight' => 0.65,
            ],
            [
                'title_en' => 'Organic Quinoa',
                'title_ar' => 'الكينوا العضوية',
                'short_name' => 'Quinoa',
                'short_description_en' => 'Premium organic quinoa - complete protein superfood',
                'short_description_ar' => 'الكينوا العضوية الممتازة - سوبرفود بروتين كامل',
                'description_en' => 'Certified organic quinoa grain, a complete protein containing all nine essential amino acids. Gluten-free, high in fiber, and perfect for healthy meals.',
                'description_ar' => 'حبوب الكينوا العضوية المعتمدة، بروتين كامل يحتوي على جميع الأحماض الأمينية الأساسية التسعة.',
                'category_id' => $foodCategory->id,
                'sub_category_id' => $healthFoodSubcategory->id,
                'regular_price' => 45.00,
                'offer_price' => 39.00,
                'net_weight' => 1000,
                'net_weight_unit' => 'g',
                'country_of_origin' => 'Peru',
                'is_halal' => true,
                'is_vegan' => true,
                'is_vegetarian' => true,
                'allergen_info' => 'Gluten-free, may contain traces of nuts',
                'storage_conditions' => 'Store in cool, dry place in airtight container',
                'package_length' => 15.0,
                'package_width' => 10.0,
                'package_height' => 25.0,
                'package_weight' => 1.1,
            ],
        ];

        foreach ($foodProducts as $index => $productData) {
            $productData['user_id'] = $user->id;
            $productData['vendor_id'] = $vendor->id;
            $productData['brand_id'] = $brands[$index % $brands->count()]->id;
            $productData['vendor_sku'] = 'FD-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $productData['barcode'] = '940' . str_pad($index + 1, 10, '0', STR_PAD_LEFT);
            $productData['vat_tax'] = 'exempted'; // Food items are VAT exempt in UAE
            $productData['discount_start_date'] = Carbon::now();
            $productData['discount_end_date'] = Carbon::now()->addDays(20);
            $productData['bbe_date'] = Carbon::now()->addYears(2);
            $productData['is_active'] = true;
            $productData['is_approved'] = true;
            $productData['status'] = 'submitted';

            Product::create($productData);
        }
    }

    private function createSportsNutritionProducts(): void
    {
        $sportsCategory = Category::where('code', 'S')->first();
        $proteinSubcategory = Category::where('code', 'PT')->first();

        $vendor = Vendor::where('code', 'VND003')->first();
        $user = User::where('email', '<EMAIL>')->first();
        $brands = Brand::where('status', 'approved')->skip(2)->limit(2)->get();

        $sportsProducts = [
            [
                'title_en' => 'Whey Protein Isolate',
                'title_ar' => 'بروتين مصل اللبن المعزول',
                'short_name' => 'Whey Isolate',
                'short_description_en' => 'Premium whey protein isolate for muscle building',
                'short_description_ar' => 'بروتين مصل اللبن المعزول الممتاز لبناء العضلات',
                'description_en' => 'Ultra-pure whey protein isolate with 90% protein content. Fast-absorbing, low in lactose and fat. Perfect for post-workout recovery and muscle building.',
                'description_ar' => 'بروتين مصل اللبن المعزول فائق النقاء بمحتوى بروتين 90%. سريع الامتصاص، منخفض اللاكتوز والدهون.',
                'category_id' => $sportsCategory->id,
                'sub_category_id' => $proteinSubcategory->id,
                'regular_price' => 299.00,
                'offer_price' => 259.00,
                'net_weight' => 2000,
                'net_weight_unit' => 'g',
                'servings' => 66,
                'flavour' => 'Vanilla',
                'country_of_origin' => 'USA',
                'is_halal' => true,
                'allergen_info' => 'Contains milk, may contain soy',
                'usage_instructions' => 'Mix 1 scoop (30g) with 200ml water or milk',
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 20.0,
                'package_width' => 15.0,
                'package_height' => 25.0,
                'package_weight' => 2.2,
            ],
        ];

        foreach ($sportsProducts as $index => $productData) {
            $productData['user_id'] = $user->id;
            $productData['vendor_id'] = $vendor->id;
            $productData['brand_id'] = $brands[$index % $brands->count()]->id;
            $productData['vendor_sku'] = 'SP-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $productData['barcode'] = '756' . str_pad($index + 1, 10, '0', STR_PAD_LEFT);
            $productData['vat_tax'] = 'standard_5';
            $productData['discount_start_date'] = Carbon::now();
            $productData['discount_end_date'] = Carbon::now()->addDays(25);
            $productData['bbe_date'] = Carbon::now()->addYears(2);
            $productData['is_active'] = true;
            $productData['is_approved'] = true;
            $productData['status'] = 'submitted';

            Product::create($productData);
        }
    }

    private function createWeightManagementProducts(): void
    {
        $weightCategory = Category::where('code', 'W')->first();
        $weightLossSubcategory = Category::where('code', 'WL')->first();

        $vendor = Vendor::where('code', 'VND001')->first();
        $user = User::where('email', '<EMAIL>')->first();
        $brands = Brand::where('status', 'approved')->limit(1)->get();

        $weightProducts = [
            [
                'title_en' => 'Green Tea Extract',
                'title_ar' => 'مستخلص الشاي الأخضر',
                'short_name' => 'Green Tea',
                'short_description_en' => 'Natural green tea extract for weight management',
                'short_description_ar' => 'مستخلص الشاي الأخضر الطبيعي لإدارة الوزن',
                'description_en' => 'Standardized green tea extract with 50% EGCG. Supports metabolism, fat burning, and provides antioxidant benefits. Natural weight management support.',
                'description_ar' => 'مستخلص الشاي الأخضر المعياري مع 50% EGCG. يدعم الأيض وحرق الدهون ويوفر فوائد مضادات الأكسدة.',
                'category_id' => $weightCategory->id,
                'sub_category_id' => $weightLossSubcategory->id,
                'regular_price' => 149.00,
                'offer_price' => 119.00,
                'net_weight' => 60,
                'net_weight_unit' => 'capsules',
                'servings' => 60,
                'country_of_origin' => 'India',
                'is_halal' => true,
                'is_vegan' => true,
                'is_vegetarian' => true,
                'allergen_info' => 'Contains caffeine',
                'usage_instructions' => 'Take 1 capsule twice daily with meals',
                'storage_conditions' => 'Store in cool, dry place',
                'package_length' => 6.0,
                'package_width' => 6.0,
                'package_height' => 11.0,
                'package_weight' => 0.12,
            ],
        ];

        foreach ($weightProducts as $index => $productData) {
            $productData['user_id'] = $user->id;
            $productData['vendor_id'] = $vendor->id;
            $productData['brand_id'] = $brands[$index % $brands->count()]->id;
            $productData['vendor_sku'] = 'WM-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
            $productData['barcode'] = '812' . str_pad($index + 1, 10, '0', STR_PAD_LEFT);
            $productData['vat_tax'] = 'standard_5';
            $productData['discount_start_date'] = Carbon::now();
            $productData['discount_end_date'] = Carbon::now()->addDays(35);
            $productData['bbe_date'] = Carbon::now()->addYears(3);
            $productData['is_active'] = true;
            $productData['is_approved'] = true;
            $productData['status'] = 'submitted';

            Product::create($productData);
        }
    }
}
