<?php

namespace App\Services;

use App\Models\VendorInformation;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class VendorInformationService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = VendorInformation::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name_tl_en,name_tl_ar']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareVendorInformationData($request);

        return VendorInformation::create($data);
    }

    private function prepareVendorInformationData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new VendorInformation())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'vendorInformation')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'vendorInformation');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'vendorInformation');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            // $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): VendorInformation
    {
        return VendorInformation::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $vendorInformation = VendorInformation::findOrFail($id);
        $updateData = $this->prepareVendorInformationData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $vendorInformation->update($updateData);

        return $vendorInformation;
    }

    public function destroy(int $id): bool
    {
        $vendorInformation = VendorInformation::findOrFail($id);
        return $vendorInformation->delete();
    }
}
