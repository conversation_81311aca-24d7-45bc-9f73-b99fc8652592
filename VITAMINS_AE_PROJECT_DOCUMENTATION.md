# Vitamins.ae Project Documentation

## Project Overview
**Project Name:** Vitamins.ae  
**Domain:** vitamins.ae  
**Project Type:** Multi-Vendor E-Commerce Platform  
**Technology Stack:** Laravel 12, PHP 8.2+, PostgreSQL, Redis, AWS S3  
**Repository:** *****************:bacbonit/uae-ecommerce-backend.git

---

## 1. Project Plan

### 1.1 Project Objectives
- Develop a comprehensive multi-vendor e-commerce platform for vitamins and health supplements
- Support Arabic and English languages (bilingual platform)
- Implement vendor onboarding and management system
- Create robust product catalog with detailed nutritional information
- Establish secure payment and order management system

### 1.2 Key Milestones
- ✅ Core API Development (Laravel 12 Backend)
- ✅ Authentication & Authorization System (Laravel Passport)
- ✅ Multi-vendor Registration & Management
- ✅ Product Catalog Management
- ✅ Order Management System
- ✅ Payment Integration Framework
- ✅ Admin Dashboard APIs
- ✅ API Documentation (Swagger)

### 1.3 Technology Architecture
- **Backend Framework:** <PERSON>vel 12
- **Database:** PostgreSQL (Primary), Redis (Cache/Sessions)
- **Authentication:** Laravel Passport (OAuth2)
- **File Storage:** AWS S3
- **API Documentation:** Swagger/OpenAPI
- **Testing:** PHPUnit
- **Queue Management:** Laravel Queues

---

## 2. Scope of Work Document

### 2.1 Included Features
- Multi-vendor registration and management
- Product catalog with variants and attributes
- Category and brand management
- Order processing and fulfillment
- Payment gateway integration
- Customer management
- Admin dashboard functionality
- Support ticket system
- Coupon and promotion management
- Inventory management
- Reporting and analytics
- Multi-language support (Arabic/English)

### 2.2 API Endpoints Coverage
- **Authentication APIs:** Login, Register, Password Reset, OTP Verification
- **Vendor Management:** EOI Registration, Vendor Information, Approval Workflow
- **Product Management:** CRUD operations, Variants, Attributes, Media
- **Order Management:** Order creation, tracking, fulfillment
- **Customer APIs:** Profile management, addresses, wishlists
- **Admin APIs:** Complete administrative control
- **Public APIs:** Home page data, product browsing, search

### 2.3 Excluded Features
- Frontend web application (API-only backend)
- Mobile applications
- Third-party marketplace integrations (Amazon, Noon)
- Advanced AI/ML recommendations (basic recommendations included)

---

## 3. Requirements Document

### 3.1 Functional Requirements
- **User Management:** Multi-role system (Admin, Vendor, Customer)
- **Product Management:** Complex product structure with variants, attributes, and media
- **Order Processing:** Complete order lifecycle management
- **Payment Processing:** Multiple payment gateway support
- **Inventory Management:** Real-time stock tracking
- **Multi-language Support:** Arabic and English content
- **Search & Filtering:** Advanced product search capabilities
- **Reporting:** Comprehensive business analytics

### 3.2 Non-Functional Requirements
- **Performance:** API response time < 200ms for standard requests
- **Scalability:** Horizontal scaling capability
- **Security:** OAuth2 authentication, role-based access control
- **Availability:** 99.9% uptime target
- **Data Integrity:** ACID compliance, backup strategies
- **Compliance:** UAE regulatory requirements for e-commerce

### 3.3 Technical Requirements
- **PHP Version:** 8.2 or higher
- **Database:** PostgreSQL 13+
- **Cache:** Redis 6+
- **Web Server:** Nginx/Apache with SSL
- **File Storage:** AWS S3 compatible storage
- **Monitoring:** Application and infrastructure monitoring

---

## 4. Functional Design Document

### 4.1 User Roles and Permissions
- **Super Admin:** Complete system access
- **Admin:** Administrative functions, vendor approval
- **Vendor:** Product management, order fulfillment
- **Customer:** Shopping, order tracking

### 4.2 Core Workflows
- **Vendor Onboarding:** EOI → Document Verification → Approval → Account Activation
- **Product Lifecycle:** Creation → Review → Approval → Publication → Sales
- **Order Processing:** Cart → Checkout → Payment → Fulfillment → Delivery
- **Support System:** Ticket Creation → Assignment → Resolution → Closure

### 4.3 Business Logic
- **Commission Structure:** Configurable commission rates per vendor/category
- **Pricing Rules:** Regular price, offer price, VAT calculations
- **Inventory Management:** Real-time stock updates, low stock alerts
- **Shipping:** Multiple shipping options, rate calculations

---

## 5. Technical Design Document

### 5.1 Architecture Overview
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Laravel App   │
│   (External)    │◄──►│   (Nginx)       │◄──►│   (Backend)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │   Redis Cache   │◄────────────┤
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │   PostgreSQL    │◄────────────┤
                       │   Database      │             │
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │   AWS S3        │◄────────────┘
                       │   File Storage  │
                       └─────────────────┘
```

### 5.2 Database Design
- **Core Tables:** users, vendors, products, orders, categories, brands
- **Relationship Tables:** product_variants, order_items, vendor_contacts
- **System Tables:** permissions, roles, audit_logs, notifications
- **Configuration Tables:** settings, dropdowns, shipping_rates

### 5.3 Security Architecture
- **Authentication:** Laravel Passport OAuth2 implementation
- **Authorization:** Spatie Laravel Permission package
- **Data Protection:** Encrypted sensitive data, secure password hashing
- **API Security:** Rate limiting, CORS configuration, input validation

---

## 6. Source Code

### 6.1 Repository Information
- **Repository URL:** *****************:bacbonit/uae-ecommerce-backend.git
- **Branch Strategy:** main (production), develop (staging), feature branches
- **Code Structure:** Standard Laravel 12 application structure

### 6.2 Key Directories
```
app/
├── Http/Controllers/     # API Controllers
├── Models/              # Eloquent Models
├── Services/            # Business Logic Services
├── Policies/            # Authorization Policies
├── Mail/                # Email Templates
└── Traits/              # Reusable Traits

database/
├── migrations/          # Database Schema
├── seeders/            # Sample Data
└── factories/          # Test Data Factories

tests/
├── Feature/            # Integration Tests
└── Unit/               # Unit Tests

config/                 # Configuration Files
routes/                 # API Routes
resources/              # Views and Assets
```

### 6.3 Code Quality Standards
- **PSR-12:** PHP coding standards compliance
- **Laravel Best Practices:** Following Laravel conventions
- **Documentation:** Comprehensive inline documentation
- **Testing:** Unit and feature test coverage

---

## 7. Master Data

### 7.1 Configuration Data
- **Categories:** Hierarchical product categorization
- **Brands:** Vendor brand information
- **Product Classes:** Product classification system
- **Dropdown Options:** System-wide dropdown configurations
- **Settings:** Application configuration parameters

### 7.2 Reference Data
- **Countries:** Country codes and names
- **Currencies:** Supported currencies (AED primary)
- **Languages:** Arabic and English language packs
- **VAT Rates:** UAE VAT configuration
- **Shipping Zones:** Delivery area definitions

### 7.3 Seed Data
- **Admin Users:** Default administrative accounts
- **Permissions:** Role-based permission matrix
- **Sample Products:** Demo product catalog
- **Test Vendors:** Sample vendor accounts

---

## 8. Transactions

### 8.1 Order Management
- **Order Creation:** Cart to order conversion
- **Payment Processing:** Multiple payment gateway support
- **Order Fulfillment:** Vendor-based fulfillment workflow
- **Shipping Integration:** Tracking and delivery management

### 8.2 Financial Transactions
- **Commission Calculations:** Automated commission processing
- **Vendor Payouts:** Payment distribution system
- **Refund Processing:** Return and refund management
- **Tax Calculations:** VAT and other tax computations

### 8.3 Inventory Transactions
- **Stock Updates:** Real-time inventory adjustments
- **Reservation System:** Cart-based stock reservation
- **Audit Trail:** Complete inventory change tracking

---

## 9. Reports

### 9.1 Business Reports
- **Sales Analytics:** Revenue, orders, conversion rates
- **Vendor Performance:** Individual vendor metrics
- **Product Analytics:** Best sellers, inventory turnover
- **Customer Insights:** User behavior and preferences

### 9.2 Operational Reports
- **Order Reports:** Order status, fulfillment metrics
- **Inventory Reports:** Stock levels, reorder alerts
- **Financial Reports:** Commission, payouts, taxes
- **System Reports:** API usage, performance metrics

### 9.3 Compliance Reports
- **Audit Logs:** System access and changes
- **Data Export:** GDPR compliance data exports
- **Regulatory Reports:** UAE commerce compliance

---

## 10. Environments Available

### 10.1 Development Environment
- **URL:** http://localhost:8000
- **Database:** Local PostgreSQL instance
- **Purpose:** Local development and testing
- **Access:** Developer machines only

### 10.2 Testing Environment
- **URL:** https://test-vitamins.ae
- **Database:** Dedicated test database
- **Purpose:** QA testing and validation
- **Access:** Development and QA teams

### 10.3 Staging Environment
- **URL:** https://staging-vitamins.ae
- **Database:** Production-like data subset
- **Purpose:** Pre-production validation
- **Access:** Stakeholders and client review

### 10.4 Production Environment
- **URL:** https://vitamins.ae
- **Database:** Production PostgreSQL cluster
- **Purpose:** Live customer-facing application
- **Access:** Authorized personnel only

---

## 11. Contact Details

### 11.1 Project Management Team
**Project Manager:**
- Name: [To be provided by MCO]
- Email: [To be provided by MCO]
- Mobile: [To be provided by MCO]

### 11.2 Technical Team
**Lead Developer:**
- Name: [To be provided by development team]
- Email: <EMAIL> (from swagger config)
- Mobile: [To be provided]

**Backend Developer:**
- Name: [To be provided]
- Email: [To be provided]
- Mobile: [To be provided]

### 11.3 Functional Team
**Business Analyst:**
- Name: [To be provided by MCO]
- Email: [To be provided by MCO]
- Mobile: [To be provided by MCO]

**QA Lead:**
- Name: [To be provided]
- Email: [To be provided]
- Mobile: [To be provided]

---

## 12. Security of the Websites

### 12.1 Authentication Security
- **OAuth2 Implementation:** Laravel Passport for secure API authentication
- **Token Management:** 
  - Access tokens expire in 15 days
  - Refresh tokens expire in 30 days
  - Personal access tokens expire in 6 months
- **Password Security:** Bcrypt hashing with configurable rounds
- **Multi-factor Authentication:** OTP verification system implemented

### 12.2 Authorization Security
- **Role-Based Access Control (RBAC):** Spatie Laravel Permission package
- **API Endpoint Protection:** Middleware-based route protection
- **Permission Matrix:** Granular permissions for different user roles
- **Rate Limiting:** API rate limiting (10 requests/minute for public endpoints)

### 12.3 Data Security
- **Input Validation:** Comprehensive request validation
- **SQL Injection Prevention:** Eloquent ORM with prepared statements
- **XSS Protection:** Input sanitization and output encoding
- **CSRF Protection:** Laravel CSRF token implementation
- **Data Encryption:** Sensitive data encryption at rest

### 12.4 Infrastructure Security
- **HTTPS Enforcement:** SSL/TLS encryption for all communications
- **CORS Configuration:** Proper cross-origin resource sharing setup
- **Security Headers:** Implementation of security headers
- **File Upload Security:** Secure file handling and validation
- **Environment Variables:** Sensitive configuration in environment files

---

## 13. SSL Certificate

### 13.1 Certificate Requirements
- **Domain Coverage:** vitamins.ae and *.vitamins.ae (wildcard certificate)
- **Certificate Type:** Extended Validation (EV) or Organization Validation (OV)
- **Certificate Authority:** Trusted CA (Let's Encrypt, DigiCert, or similar)
- **Validity Period:** Minimum 1 year, renewable

### 13.2 Implementation Details
- **Web Server Configuration:** Nginx/Apache SSL configuration
- **Certificate Installation:** Proper certificate chain installation
- **HTTP to HTTPS Redirect:** Automatic redirection for all HTTP requests
- **HSTS Implementation:** HTTP Strict Transport Security headers
- **Certificate Monitoring:** Automated certificate expiry monitoring

### 13.3 Certificate Management
- **Renewal Process:** Automated certificate renewal setup
- **Backup Certificates:** Secure backup of certificate files
- **Certificate Validation:** Regular SSL certificate validation checks
- **Emergency Procedures:** Certificate replacement procedures

---

## 14. Domain Name and URL Ownership

### 14.1 Domain Information
- **Primary Domain:** vitamins.ae
- **Domain Owner:** MCO (as specified in requirements)
- **Domain Registrar:** [To be confirmed by MCO]
- **DNS Management:** [To be configured by MCO]

### 14.2 Subdomain Structure
- **Production:** vitamins.ae
- **Staging:** staging.vitamins.ae
- **Testing:** test.vitamins.ae
- **API Documentation:** docs.vitamins.ae
- **Admin Panel:** admin.vitamins.ae

### 14.3 DNS Configuration
- **A Records:** Point to production server IP
- **CNAME Records:** Subdomain configurations
- **MX Records:** Email server configuration
- **TXT Records:** Domain verification and SPF records

---

## 15. UAT Process

### 15.1 UAT Planning
- **UAT Environment:** Dedicated staging environment with production-like data
- **UAT Timeline:** 2-3 weeks for comprehensive testing
- **UAT Team:** Business users, stakeholders, and end-user representatives
- **UAT Scope:** All functional requirements and user workflows

### 15.2 UAT Phases
**Phase 1: Core Functionality Testing**
- User registration and authentication
- Vendor onboarding process
- Product catalog management
- Basic order processing

**Phase 2: Advanced Features Testing**
- Payment gateway integration
- Multi-language functionality
- Admin dashboard features
- Reporting and analytics

**Phase 3: Integration Testing**
- Third-party service integrations
- Email notifications
- File upload and management
- API performance testing

### 15.3 UAT Deliverables
- **Test Cases:** Comprehensive UAT test case documentation
- **Test Results:** Detailed test execution results
- **Bug Reports:** Issue tracking and resolution
- **Sign-off Documentation:** Formal UAT approval documentation

---

## 16. Test Scripts

### 16.1 Automated Test Suite
**Unit Tests:**
- Model validation tests
- Service layer tests
- Helper function tests
- Policy authorization tests

**Feature Tests:**
- API endpoint tests
- Authentication flow tests
- Business workflow tests
- Integration tests

### 16.2 Test Execution
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run with coverage
php artisan test --coverage

# Run specific test file
php artisan test tests/Feature/HomePageApiTest.php
```

### 16.3 Test Coverage
- **Current Coverage:** [To be measured]
- **Target Coverage:** 80% minimum
- **Critical Path Coverage:** 95% for core business logic
- **Test Documentation:** Comprehensive test case documentation

### 16.4 Manual Test Scripts
- **User Registration Flow:** Step-by-step user registration testing
- **Vendor Onboarding:** Complete vendor EOI and approval process
- **Product Management:** Product creation, editing, and approval
- **Order Processing:** End-to-end order placement and fulfillment
- **Payment Testing:** Payment gateway integration testing

---

## 17. Data Security and Backup, Data Access Policies

### 17.1 Data Security Measures
**Encryption:**
- Data at rest: Database encryption
- Data in transit: TLS 1.3 encryption
- Application-level encryption for sensitive fields
- Secure key management system

**Access Controls:**
- Database access restricted to authorized personnel
- Application-level access controls (RBAC)
- Network-level security (VPC, security groups)
- Regular access review and audit

### 17.2 Backup Strategy
**Database Backups:**
- Daily automated backups
- Weekly full database backups
- Monthly archive backups
- Point-in-time recovery capability

**File Storage Backups:**
- AWS S3 versioning enabled
- Cross-region replication
- Lifecycle policies for cost optimization
- Regular backup integrity testing

**Backup Retention:**
- Daily backups: 30 days retention
- Weekly backups: 12 weeks retention
- Monthly backups: 12 months retention
- Annual backups: 7 years retention

### 17.3 Data Access Policies
**Access Principles:**
- Principle of least privilege
- Need-to-know basis access
- Regular access review and certification
- Segregation of duties

**Data Classification:**
- Public: Marketing content, product information
- Internal: Business data, analytics
- Confidential: Customer data, financial information
- Restricted: Payment data, personal identification

**Access Controls:**
- Role-based access control (RBAC)
- Multi-factor authentication for sensitive access
- Regular password rotation
- Session timeout policies

### 17.4 Compliance and Governance
**Data Protection:**
- GDPR compliance for EU customers
- UAE Data Protection Law compliance
- PCI DSS compliance for payment data
- Regular compliance audits

**Data Retention:**
- Customer data: As per legal requirements
- Transaction data: 7 years minimum
- Log data: 2 years retention
- Backup data: As per backup retention policy

---

## 18. Credentials on Environments and Servers

### 18.1 Environment Access Credentials
**Development Environment:**
- Database: Local PostgreSQL credentials
- Redis: Local Redis instance
- AWS S3: Development bucket credentials
- Email: Development SMTP settings

**Testing Environment:**
- Database: Test database credentials (encrypted)
- Redis: Test Redis instance
- AWS S3: Test bucket credentials
- Email: Test email service

**Staging Environment:**
- Database: Staging database credentials (encrypted)
- Redis: Staging Redis cluster
- AWS S3: Staging bucket credentials
- Email: Staging email service

**Production Environment:**
- Database: Production database credentials (encrypted)
- Redis: Production Redis cluster
- AWS S3: Production bucket credentials
- Email: Production email service

### 18.2 Server Access Management
**SSH Access:**
- Key-based authentication only
- No password authentication
- Regular key rotation
- Access logging and monitoring

**Application Credentials:**
- Environment-specific .env files
- Encrypted credential storage
- Regular credential rotation
- Secure credential distribution

### 18.3 Third-Party Service Credentials
**AWS Services:**
- IAM roles and policies
- Access key rotation
- Service-specific permissions
- CloudTrail logging

**Email Services:**
- SMTP credentials
- API keys for email services
- Rate limiting configurations
- Bounce and complaint handling

### 18.4 Credential Security Practices
**Storage:**
- Environment variables for sensitive data
- Encrypted configuration files
- Secure key management systems
- No hardcoded credentials in source code

**Access Management:**
- Regular credential audits
- Immediate revocation for terminated access
- Temporary credentials for contractors
- Emergency access procedures

---

## 19. No Hard Coding Confirmation

### 19.1 Configuration Management
**Environment Variables:**
All configuration values are managed through environment variables in .env files:
- Database connections
- API keys and secrets
- Service endpoints
- Feature flags
- Cache configurations

**Configuration Files:**
Laravel configuration files use env() helper functions:
```php
// Example from config/app.php
'name' => env('APP_NAME', 'Laravel'),
'url' => env('APP_URL', 'http://localhost'),
'timezone' => env('APP_TIMEZONE', 'UTC'),
```

### 19.2 Dynamic Configuration Sources
**Database-Driven Configuration:**
- Settings table for application configurations
- Dropdown options for dynamic values
- Feature toggles stored in database
- User preferences and customizations

**API-Based Configuration:**
- External service configurations
- Dynamic pricing rules
- Shipping rate calculations
- Tax rate configurations

### 19.3 Code Review Practices
**Hard Coding Prevention:**
- Code review checklist includes hard coding checks
- Automated linting rules to detect hard-coded values
- Regular code audits for configuration compliance
- Developer training on configuration best practices

### 19.4 Verification Process
**Automated Checks:**
- Static code analysis for hard-coded values
- Environment variable validation
- Configuration drift detection
- Deployment validation scripts

---

## 20. Dynamic Variables

### 20.1 Variable Management System
**Configuration Variables:**
All system variables are managed dynamically through:
- Environment configuration files (.env)
- Database settings table
- Dropdown options system
- API configuration endpoints

**Business Logic Variables:**
- Commission rates per vendor/category
- Tax rates and calculations
- Shipping rates and zones
- Discount and promotion rules

### 20.2 Master Data Management
**Dropdown System:**
Dynamic dropdown options managed through database:
- Categories and subcategories
- Product attributes and variants
- Shipping options
- Payment methods
- User roles and permissions

**Settings Management:**
Application settings stored in database:
- Site configuration
- Email templates
- Notification preferences
- Feature flags
- Integration settings

### 20.3 Runtime Configuration
**Dynamic Loading:**
- Configuration cached for performance
- Real-time updates without deployment
- A/B testing configurations
- User-specific customizations

**API-Driven Updates:**
- Admin panel for configuration management
- Bulk configuration updates
- Configuration versioning
- Rollback capabilities

### 20.4 Variable Update Process
**Master Data Updates:**
- Admin interface for dropdown management
- Bulk import/export capabilities
- Version control for configuration changes
- Audit trail for all modifications

**Deployment Process:**
- Environment-specific configurations
- Configuration validation before deployment
- Automated configuration synchronization
- Zero-downtime configuration updates

---

## 21. Variables Updated via Master Data

### 21.1 Master Data Tables
**Dropdowns Table:**
Centralized management of all dropdown options:
- Categories and subcategories
- Product attributes
- Shipping methods
- Payment options
- Status values

**Settings Table:**
Application-wide settings:
- Site configuration
- Email settings
- Payment gateway configurations
- Feature toggles
- Business rules

### 21.2 Dynamic Content Management
**Multi-language Content:**
- Product descriptions (Arabic/English)
- Category names and descriptions
- Email templates
- System messages
- Help content

**Business Rules:**
- Commission structures
- Tax calculations
- Shipping rules
- Discount policies
- Approval workflows

### 21.3 Admin Management Interface
**Configuration Management:**
- Web-based admin interface for all configurations
- Bulk update capabilities
- Import/export functionality
- Real-time preview of changes
- Approval workflow for critical changes

**Data Validation:**
- Input validation for all master data
- Business rule validation
- Dependency checking
- Data integrity constraints

### 21.4 Update Mechanisms
**Real-time Updates:**
- Cache invalidation on updates
- Event-driven configuration refresh
- API endpoints for configuration retrieval
- WebSocket notifications for real-time updates

**Audit and Versioning:**
- Complete audit trail for all changes
- Version control for configuration data
- Rollback capabilities
- Change approval workflows

---

## Conclusion

This comprehensive documentation covers all aspects of the Vitamins.ae project as requested. The system is built with modern Laravel practices, ensuring scalability, security, and maintainability. All configurations are dynamic and managed through master data, with no hard-coded values in the application code.

For any additional information or clarification on specific aspects of the project, please contact the project team using the contact details provided in Section 11.

---

**Document Version:** 1.0  
**Last Updated:** June 24, 2025  
**Prepared By:** Development Team  
**Reviewed By:** [To be filled by MCO]  
**Approved By:** [To be filled by MCO]
